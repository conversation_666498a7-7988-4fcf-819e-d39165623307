class VehicleEntry {
    constructor() {
        this.isExitMode = false;
        this.currentVehicle = null;
        // 中国省份缩写数据
        this.provinces = [
            '京', '津', '冀', '晋', '蒙', '辽', '吉', '黑',
            '沪', '苏', '浙', '皖', '闽', '赣', '鲁', '豫',
            '鄂', '湘', '粤', '桂', '琼', '渝', '川', '贵',
            '云', '藏', '陕', '甘', '青', '宁', '新', '港',
            '澳', '台'
        ];
        // 字母选项
        this.letters = [
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 
            'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 
            'W', 'X', 'Y', 'Z'
        ];
        this.init();
    }

    init() {
        this.checkModeFromUrl();
        this.setupEventListeners();
        this.loadParkingSelector();
        this.loadLicensePlateOptions();
        this.updateCurrentTime();
        this.loadRecentRecords();
        this.startTimeUpdates();
    }

    checkModeFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        const action = urlParams.get('action');
        
        if (action === 'exit') {
            this.switchToExitMode();
        }
    }

    setupEventListeners() {
        // 模式切换按钮
        document.getElementById('switchModeBtn').addEventListener('click', () => {
            this.toggleMode();
        });

        // 表单提交
        document.getElementById('vehicleForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSubmit();
        });

        // 车牌分段输入事件监听
        this.setupLicensePlateListeners();

        // 监听停车场切换事件
        window.addEventListener('parkingLotChanged', () => {
            this.loadParkingSelector();
        });
    }

    setupLicensePlateListeners() {
        const provincePart = document.getElementById('provincePart');
        const letterPart = document.getElementById('letterPart');
        const numberPart = document.getElementById('numberPart');

        // 监听输入变化，实时更新完整车牌号
        [provincePart, letterPart, numberPart].forEach(element => {
            element.addEventListener('input', () => {
                this.updateFullLicensePlate();
            });
            element.addEventListener('change', () => {
                this.updateFullLicensePlate();
            });
        });

        // 在出场模式下，当完整车牌号变化时自动搜索
        numberPart.addEventListener('blur', () => {
            if (this.isExitMode) {
                this.searchVehicle();
            }
        });

        // 回车键触发搜索
        numberPart.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && this.isExitMode) {
                e.preventDefault();
                this.searchVehicle();
            }
        });

        // 设置虚拟键盘监听器
        this.setupVirtualKeyboard();
    }

    setupVirtualKeyboard() {
        const keyboardToggle = document.getElementById('keyboardToggle');
        const virtualKeyboard = document.getElementById('virtualKeyboard');
        
        // 切换键盘显示
        keyboardToggle.addEventListener('click', () => {
            virtualKeyboard.classList.toggle('hidden');
        });

        // 键盘按钮点击事件
        virtualKeyboard.addEventListener('click', (e) => {
            if (e.target.classList.contains('key-btn')) {
                const value = e.target.getAttribute('data-value');
                const action = e.target.getAttribute('data-action');
                
                if (action) {
                    this.handleKeyboardAction(action);
                } else if (value) {
                    this.handleKeyboardInput(value);
                }
            }
        });

        // 点击页面其他区域关闭键盘
        document.addEventListener('click', (e) => {
            if (!virtualKeyboard.contains(e.target) && 
                !keyboardToggle.contains(e.target) &&
                !virtualKeyboard.classList.contains('hidden')) {
                virtualKeyboard.classList.add('hidden');
            }
        });

        // 阻止键盘内部点击事件冒泡
        virtualKeyboard.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    handleKeyboardInput(value) {
        const provincePart = document.getElementById('provincePart');
        const letterPart = document.getElementById('letterPart');
        const numberPart = document.getElementById('numberPart');
        
        // 判断输入类型并插入到对应位置
        if (this.provinces.includes(value)) {
            // 省份输入
            provincePart.value = value;
            letterPart.focus();
        } else if (this.letters.includes(value)) {
            // 字母输入
            letterPart.value = value;
            numberPart.focus();
        } else if (/\d/.test(value)) {
            // 数字输入
            if (numberPart.value.length < 6) {
                numberPart.value += value;
            }
        }
        
        this.updateFullLicensePlate();
    }

    handleKeyboardAction(action) {
        const numberPart = document.getElementById('numberPart');
        
        switch (action) {
            case 'backspace':
                // 退格键
                if (numberPart.value.length > 0) {
                    numberPart.value = numberPart.value.slice(0, -1);
                    this.updateFullLicensePlate();
                }
                break;
                
            case 'clear':
                // 清空
                this.clearLicensePlate();
                break;
                
            case 'done':
                // 完成，关闭键盘
                document.getElementById('virtualKeyboard').classList.add('hidden');
                if (this.isExitMode) {
                    this.searchVehicle();
                }
                break;
        }
    }

    updateFullLicensePlate() {
        const province = document.getElementById('provincePart').value;
        const letter = document.getElementById('letterPart').value;
        const number = document.getElementById('numberPart').value;
        
        const fullPlate = province + letter + number;
        document.getElementById('licensePlate').value = fullPlate.toUpperCase();
        
        // 可视化验证反馈
        this.validateLicensePlateFormat();
    }

    validateLicensePlateFormat() {
        const province = document.getElementById('provincePart').value;
        const letter = document.getElementById('letterPart').value;
        const number = document.getElementById('numberPart').value;
        
        const elements = [
            document.getElementById('provincePart'),
            document.getElementById('letterPart'),
            document.getElementById('numberPart')
        ];
        
        // 移除之前的验证样式
        elements.forEach(el => {
            el.classList.remove('border-red-500', 'border-green-500');
        });
        
        // 省份验证
        if (province && this.provinces.includes(province)) {
            document.getElementById('provincePart').classList.add('border-green-500');
        } else if (province) {
            document.getElementById('provincePart').classList.add('border-red-500');
        }
        
        // 字母验证
        if (letter && this.letters.includes(letter)) {
            document.getElementById('letterPart').classList.add('border-green-500');
        } else if (letter) {
            document.getElementById('letterPart').classList.add('border-red-500');
        }
        
        // 数字部分验证（简单长度检查）
        if (number && number.length >= 5) {
            document.getElementById('numberPart').classList.add('border-green-500');
        } else if (number) {
            document.getElementById('numberPart').classList.add('border-red-500');
        }
    }

    setLicensePlateFromFull(fullPlate) {
        if (!fullPlate || fullPlate.length < 2) return;
        
        const province = fullPlate.charAt(0);
        const letter = fullPlate.charAt(1);
        const number = fullPlate.substring(2);
        
        document.getElementById('provincePart').value = province;
        document.getElementById('letterPart').value = letter;
        document.getElementById('numberPart').value = number;
        
        this.updateFullLicensePlate();
    }

    clearLicensePlate() {
        document.getElementById('provincePart').value = '';
        document.getElementById('letterPart').value = '';
        document.getElementById('numberPart').value = '';
        document.getElementById('licensePlate').value = '';
    }

    toggleMode() {
        if (this.isExitMode) {
            this.switchToEntryMode();
        } else {
            this.switchToExitMode();
        }
    }

    switchToEntryMode() {
        this.isExitMode = false;
        this.currentVehicle = null;
        
        document.getElementById('pageTitle').textContent = '车辆入场';
        document.getElementById('pageSubtitle').textContent = '记录车辆进入停车场';
        document.getElementById('formTitle').textContent = '车辆入场登记';
        document.getElementById('submitText').textContent = '确认入场';
        document.getElementById('switchModeBtn').textContent = '切换到出场';
        document.getElementById('exitSection').classList.add('hidden');
        document.getElementById('entryTimeField').classList.remove('hidden');
        
        this.clearLicensePlate();
        document.getElementById('provincePart').focus();
    }

    switchToExitMode() {
        this.isExitMode = true;
        this.currentVehicle = null;
        
        document.getElementById('pageTitle').textContent = '车辆出场';
        document.getElementById('pageSubtitle').textContent = '处理车辆离开并计算费用';
        document.getElementById('formTitle').textContent = '车辆出场结算';
        document.getElementById('submitText').textContent = '确认出场并收费';
        document.getElementById('switchModeBtn').textContent = '切换到入场';
        document.getElementById('exitSection').classList.remove('hidden');
        document.getElementById('entryTimeField').classList.add('hidden');
        
        this.clearCalculation();
        document.getElementById('provincePart').focus();
    }

    loadLicensePlateOptions() {
        // 加载省份选项
        const provinceSelect = document.getElementById('provincePart');
        this.provinces.forEach(province => {
            const option = document.createElement('option');
            option.value = province;
            option.textContent = province;
            provinceSelect.appendChild(option);
        });

        // 加载字母选项
        const letterSelect = document.getElementById('letterPart');
        this.letters.forEach(letter => {
            const option = document.createElement('option');
            option.value = letter;
            option.textContent = letter;
            letterSelect.appendChild(option);
        });
    }

    loadParkingSelector() {
        const container = document.getElementById('parkingSelector');
        const parkingLots = storage.getParkingLots();
        
        if (parkingLots.length === 0) {
            container.innerHTML = `
                <div class="text-red-400 text-sm">
                    暂无停车场，请先<a href="parking-management.html" class="underline">添加停车场</a>
                </div>
            `;
            return;
        }

        let html = '<select class="input-glass w-full" id="parkingSelect" required>';
        html += '<option value="">选择停车场</option>';
        
        const currentParkingId = storage.getCurrentParkingLotId();
        
        parkingLots.forEach(parking => {
            const stats = storage.getParkingLotStats(parking.id);
            const availableText = stats ? ` (空闲: ${stats.availableSpaces})` : '';
            const selected = parking.id === currentParkingId ? 'selected' : '';
            html += `<option value="${parking.id}" ${selected}>${parking.name}${availableText}</option>`;
        });
        
        html += '</select>';
        container.innerHTML = html;
    }

    updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN');
        
        document.getElementById('currentTime').textContent = timeString;
        document.getElementById('exitTime').textContent = timeString;
        
        if (this.currentVehicle) {
            this.calculateFee();
        }
    }

    startTimeUpdates() {
        setInterval(() => {
            this.updateCurrentTime();
        }, 1000);
    }

    searchVehicle() {
        const licensePlate = document.getElementById('licensePlate').value.trim().toUpperCase();
        
        if (!licensePlate) {
            componentLoader.showToast('请输入车牌号码', 'warning');
            return;
        }

        const vehicles = storage.getVehicles();
        const parkedVehicle = vehicles.find(v => 
            v.licensePlate === licensePlate && v.status === 'parked'
        );

        if (!parkedVehicle) {
            componentLoader.showToast('未找到该车辆的入场记录', 'error');
            this.clearCalculation();
            return;
        }

        this.currentVehicle = parkedVehicle;
        
        // 设置车牌号分段显示
        this.setLicensePlateFromFull(parkedVehicle.licensePlate);
        
        // 显示入场时间
        const entryTime = new Date(parkedVehicle.entryTime);
        document.getElementById('entryTimeDisplay').textContent = entryTime.toLocaleString('zh-CN');
        
        // 设置停车场选择
        const parkingSelect = document.getElementById('parkingSelect');
        if (parkingSelect) {
            parkingSelect.value = parkedVehicle.parkingLotId;
            parkingSelect.disabled = true;
        }

        // 计算费用
        this.calculateFee();
        
        componentLoader.showToast('找到车辆记录，开始计费', 'success');
    }

    calculateFee() {
        if (!this.currentVehicle) return;

        const entryTime = new Date(this.currentVehicle.entryTime);
        const exitTime = new Date();
        
        // 获取停车场费率
        const parkingLots = storage.getParkingLots();
        const parking = parkingLots.find(p => p.id === this.currentVehicle.parkingLotId);
        const hourlyRate = parking ? parking.hourlyRate : 5;

        // 计算费用
        const amount = storage.calculateParkingFee(entryTime, exitTime, hourlyRate);
        
        // 计算停车时长
        const durationMs = exitTime - entryTime;
        const hours = Math.floor(durationMs / (1000 * 60 * 60));
        const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

        // 更新显示
        document.getElementById('parkingDuration').textContent = `${hours}小时${minutes}分钟`;
        document.getElementById('hourlyRate').textContent = `¥${hourlyRate.toFixed(2)}/小时`;
        document.getElementById('totalAmount').textContent = `¥${amount.toFixed(2)}`;
    }

    clearCalculation() {
        document.getElementById('entryTimeDisplay').textContent = '--:--:--';
        document.getElementById('parkingDuration').textContent = '0小时0分钟';
        document.getElementById('hourlyRate').textContent = '¥0.00/小时';
        document.getElementById('totalAmount').textContent = '¥0.00';
        
        const parkingSelect = document.getElementById('parkingSelect');
        if (parkingSelect) {
            parkingSelect.disabled = false;
            parkingSelect.value = '';
        }
    }

    async handleSubmit() {
        const licensePlate = document.getElementById('licensePlate').value.trim().toUpperCase();
        const parkingSelect = document.getElementById('parkingSelect');
        
        if (!licensePlate) {
            componentLoader.showToast('请输入完整的车牌号码', 'error');
            return;
        }

        // 基本车牌格式验证
        if (licensePlate.length < 7) {
            componentLoader.showToast('车牌号码长度不足', 'error');
            return;
        }

        if (!parkingSelect.value && !this.isExitMode) {
            componentLoader.showToast('请选择停车场', 'error');
            return;
        }

        try {
            if (this.isExitMode) {
                await this.processExit(licensePlate);
            } else {
                await this.processEntry(licensePlate, parkingSelect.value);
            }
        } catch (error) {
            console.error('处理失败:', error);
            componentLoader.showToast('操作失败，请重试', 'error');
        }
    }

    async processEntry(licensePlate, parkingLotId) {
        // 检查车辆是否已经入场
        const vehicles = storage.getVehicles();
        const existingVehicle = vehicles.find(v => 
            v.licensePlate === licensePlate && v.status === 'parked'
        );

        if (existingVehicle) {
            componentLoader.showToast('该车辆已在停车场内', 'error');
            return;
        }

        // 检查停车场是否有空位
        const stats = storage.getParkingLotStats(parkingLotId);
        if (stats && stats.availableSpaces <= 0) {
            componentLoader.showToast('该停车场已满', 'error');
            return;
        }

        // 添加车辆记录
        const vehicle = storage.addVehicle({
            licensePlate: licensePlate,
            parkingLotId: parkingLotId
        });

        componentLoader.showToast(`车辆 ${licensePlate} 入场成功`, 'success');
        
        // 清空表单
        this.clearLicensePlate();
        parkingSelect.value = '';
        document.getElementById('provincePart').focus();

        // 刷新最近记录
        this.loadRecentRecords();
    }

    async processExit(licensePlate) {
        if (!this.currentVehicle) {
            componentLoader.showToast('请先搜索车辆', 'error');
            return;
        }

        const exitTime = new Date();
        const amount = parseFloat(document.getElementById('totalAmount').textContent.replace('¥', ''));

        // 更新车辆状态
        storage.updateVehicle(this.currentVehicle.id, {
            exitTime: exitTime.toISOString(),
            status: 'exited'
        });

        // 添加交易记录
        storage.addTransaction({
            licensePlate: this.currentVehicle.licensePlate,
            parkingLotId: this.currentVehicle.parkingLotId,
            entryTime: this.currentVehicle.entryTime,
            exitTime: exitTime.toISOString(),
            amount: amount,
            status: 'completed'
        });

        componentLoader.showToast(`车辆 ${licensePlate} 出场成功，收费 ¥${amount.toFixed(2)}`, 'success');

        // 清空表单
        this.clearCalculation();
        this.clearLicensePlate();
        this.currentVehicle = null;
        
        const parkingSelect = document.getElementById('parkingSelect');
        if (parkingSelect) {
            parkingSelect.disabled = false;
            parkingSelect.value = '';
        }

        // 刷新最近记录
        this.loadRecentRecords();
    }

    loadRecentRecords() {
        const container = document.getElementById('recentRecords');
        const vehicles = storage.getVehicles().slice(0, 10); // 最新10条记录
        
        if (vehicles.length === 0) {
            container.innerHTML = '<div class="text-center py-4 text-gray-500">暂无记录</div>';
            return;
        }

        let html = '';
        vehicles.forEach(vehicle => {
            const entryTime = new Date(vehicle.entryTime);
            const status = vehicle.status === 'parked' ? '🟢 停放中' : '🔴 已出场';
            const timeText = entryTime.toLocaleString('zh-CN');
            
            html += `
                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <span class="text-2xl">🚗</span>
                        <div>
                            <div class="font-mono text-sm">${vehicle.licensePlate}</div>
                            <div class="text-xs text-gray-400">${timeText}</div>
                        </div>
                    </div>
                    <span class="text-sm ${vehicle.status === 'parked' ? 'text-green-400' : 'text-gray-400'}">
                        ${status}
                    </span>
                </div>
            `;
        });

        container.innerHTML = html;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 组件加载完成后初始化
    setTimeout(() => {
        // 初始化车辆入场管理器
        window.vehicleEntry = new VehicleEntry();
        
        // 如果停车场切换器存在，重新初始化它
        if (typeof ParkingSwitcher !== 'undefined' && typeof storage !== 'undefined' && typeof componentLoader !== 'undefined') {
            if (!window.parkingSwitcher) {
                window.parkingSwitcher = new ParkingSwitcher();
            }
        }
    }, 100);
});